using Microsoft.AspNetCore.Mvc;
using System.Collections.Concurrent;
using Microsoft.AspNetCore.SignalR;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Controllers;

/// <summary>
/// Controller for managing bulk seeding operations and sessions
/// Integrates with existing migration infrastructure
/// </summary>
[ApiController]
[Route("api/bulk-seeder")]
[Produces("application/json")]
public class BulkSeederController : ControllerBase
{
    private readonly ILogger<BulkSeederController> _logger;
    private readonly IBulkSeederService _bulkSeederService;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly IHubContext<MigrationHub> _hubContext;

    private static readonly ConcurrentDictionary<Guid, SeederSessionInfo> _activeSessions = new();

    public BulkSeederController(
        ILogger<BulkSeederController> logger,
        IBulkSeederService bulkSeederService,
        IEnvironmentConfigurationService environmentService,
        IHubContext<MigrationHub> hubContext)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _bulkSeederService = bulkSeederService ?? throw new ArgumentNullException(nameof(bulkSeederService));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        _hubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));
    }

    /// <summary>
    /// Creates a new seeding session
    /// </summary>
    /// <param name="request">Seeding session creation request</param>
    /// <returns>Created seeding session information</returns>
    /// <response code="201">Returns the created seeding session</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost("sessions")]
    [ProducesResponseType(typeof(SeederSessionInfo), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<SeederSessionInfo>> CreateSession([FromBody] CreateSeederSessionRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(new ValidationProblemDetails(ModelState));
            }

            _logger.LogInformation("Creating seeding session for dealer {DealerId}, customer {CustomerName}, drivers: {DriversCount}, vehicles: {VehiclesCount}",
                request.DealerId, request.CustomerName, request.DriversCount, request.VehiclesCount);

            // Set current environment if specified
            if (!string.IsNullOrWhiteSpace(request.Environment))
            {
                _environmentService.SetCurrentEnvironment(request.Environment);
            }

            var sessionId = Guid.NewGuid();
            var sessionInfo = new SeederSessionInfo
            {
                Id = sessionId,
                DealerId = request.DealerId,
                CustomerName = request.CustomerName,
                DriversCount = request.DriversCount,
                VehiclesCount = request.VehiclesCount,
                Environment = _environmentService.CurrentEnvironmentKey,
                Status = "Created",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = GetCurrentUser()
            };

            _activeSessions[sessionId] = sessionInfo;

            _logger.LogInformation("Created seeding session {SessionId} for environment {Environment}",
                sessionId, sessionInfo.Environment);

            // Add to SignalR group for real-time updates
            var connectionId = HttpContext.Connection.Id;
            if (!string.IsNullOrEmpty(connectionId))
            {
                await _hubContext.Groups.AddToGroupAsync(connectionId, sessionId.ToString());
            }

            return CreatedAtAction(nameof(GetSession), new { sessionId }, sessionInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create seeding session");
            return Problem(
                title: "Internal Server Error",
                detail: "An error occurred while creating the seeding session",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Executes a seeding session
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>Execution result</returns>
    /// <response code="202">Seeding execution started</response>
    /// <response code="404">Session not found</response>
    /// <response code="400">Session cannot be executed</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("sessions/{sessionId}/execute")]
    [ProducesResponseType(typeof(object), StatusCodes.Status202Accepted)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult> ExecuteSession(Guid sessionId)
    {
        try
        {
            if (!_activeSessions.TryGetValue(sessionId, out var sessionInfo))
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Session Not Found",
                    Detail = $"Seeding session {sessionId} was not found",
                    Status = StatusCodes.Status404NotFound
                });
            }

            if (sessionInfo.Status != "Created")
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Session State",
                    Detail = $"Session {sessionId} is in state '{sessionInfo.Status}' and cannot be executed",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogInformation("Starting execution of seeding session {SessionId}", sessionId);

            // Update session status
            sessionInfo.Status = "Running";
            sessionInfo.StartedAt = DateTime.UtcNow;

            // Set environment for the session
            if (!string.IsNullOrWhiteSpace(sessionInfo.Environment))
            {
                _environmentService.SetCurrentEnvironment(sessionInfo.Environment);
            }

            // Prepare seeding options
            var options = new SeederOptions
            {
                DriversCount = sessionInfo.DriversCount,
                VehiclesCount = sessionInfo.VehiclesCount,
                DealerId = sessionInfo.DealerId,
                CustomerName = sessionInfo.CustomerName,
                DryRun = false,
                GenerateData = true,
                Interactive = false
            };

            // Execute seeding in background
            _ = Task.Run(async () =>
            {
                try
                {
                    var result = await _bulkSeederService.ExecuteSeederAsync(options);

                    // Update session with results
                    sessionInfo.Status = result.Success ? "Completed" : "Failed";
                    sessionInfo.CompletedAt = DateTime.UtcNow;
                    sessionInfo.Duration = result.Duration;
                    sessionInfo.TotalRows = result.TotalRows;
                    sessionInfo.ProcessedRows = result.ProcessedRows;
                    sessionInfo.SuccessfulRows = result.SuccessfulRows;
                    sessionInfo.FailedRows = result.FailedRows;
                    sessionInfo.Errors = result.Errors;
                    sessionInfo.Summary = result.Summary;

                    _logger.LogInformation("Seeding session {SessionId} completed with status {Status}",
                        sessionId, sessionInfo.Status);

                    // Send completion notification
                    await _hubContext.Clients.Group(sessionId.ToString())
                        .SendAsync("SeederSessionCompleted", sessionInfo);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Seeding session {SessionId} failed with exception", sessionId);

                    sessionInfo.Status = "Failed";
                    sessionInfo.CompletedAt = DateTime.UtcNow;
                    sessionInfo.Errors = new List<string> { ex.Message };
                    sessionInfo.Summary = $"Seeding failed: {ex.Message}";

                    // Send failure notification
                    await _hubContext.Clients.Group(sessionId.ToString())
                        .SendAsync("SeederSessionFailed", sessionInfo);
                }
            });

            return Accepted(new
            {
                sessionId,
                status = sessionInfo.Status,
                message = "Seeding execution started. Use SignalR or polling to monitor progress."
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute seeding session {SessionId}", sessionId);
            return Problem(
                title: "Internal Server Error",
                detail: "An error occurred while executing the seeding session",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Gets information about a specific seeding session
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>Session information</returns>
    /// <response code="200">Returns session information</response>
    /// <response code="404">Session not found</response>
    [HttpGet("sessions/{sessionId}")]
    [ProducesResponseType(typeof(SeederSessionInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    public ActionResult<SeederSessionInfo> GetSession(Guid sessionId)
    {
        if (!_activeSessions.TryGetValue(sessionId, out var sessionInfo))
        {
            return NotFound(new ProblemDetails
            {
                Title = "Session Not Found",
                Detail = $"Seeding session {sessionId} was not found",
                Status = StatusCodes.Status404NotFound
            });
        }

        return Ok(sessionInfo);
    }

    /// <summary>
    /// Gets all active seeding sessions
    /// </summary>
    /// <returns>List of active sessions</returns>
    /// <response code="200">Returns list of sessions</response>
    [HttpGet("sessions")]
    [ProducesResponseType(typeof(IEnumerable<SeederSessionInfo>), StatusCodes.Status200OK)]
    public ActionResult<IEnumerable<SeederSessionInfo>> GetSessions()
    {
        var sessions = _activeSessions.Values
            .OrderByDescending(s => s.CreatedAt)
            .Take(50) // Limit to recent sessions
            .ToList();

        return Ok(sessions);
    }

    /// <summary>
    /// Cancels a running seeding session
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>Cancellation result</returns>
    /// <response code="200">Session cancelled successfully</response>
    /// <response code="404">Session not found</response>
    /// <response code="400">Session cannot be cancelled</response>
    [HttpPost("sessions/{sessionId}/cancel")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> CancelSession(Guid sessionId)
    {
        if (!_activeSessions.TryGetValue(sessionId, out var sessionInfo))
        {
            return NotFound(new ProblemDetails
            {
                Title = "Session Not Found",
                Detail = $"Seeding session {sessionId} was not found",
                Status = StatusCodes.Status404NotFound
            });
        }

        if (sessionInfo.Status != "Running")
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Invalid Session State",
                Detail = $"Session {sessionId} is in state '{sessionInfo.Status}' and cannot be cancelled",
                Status = StatusCodes.Status400BadRequest
            });
        }

        try
        {
            sessionInfo.Status = "Cancelled";
            sessionInfo.CompletedAt = DateTime.UtcNow;
            sessionInfo.Summary = "Session cancelled by user";

            _logger.LogInformation("Seeding session {SessionId} cancelled", sessionId);

            // Send cancellation notification
            await _hubContext.Clients.Group(sessionId.ToString())
                .SendAsync("SeederSessionCancelled", sessionInfo);

            return Ok(new
            {
                sessionId,
                status = sessionInfo.Status,
                message = "Session cancelled successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel seeding session {SessionId}", sessionId);
            return Problem(
                title: "Internal Server Error",
                detail: "An error occurred while cancelling the session",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Deletes a completed seeding session
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>Deletion result</returns>
    /// <response code="204">Session deleted successfully</response>
    /// <response code="404">Session not found</response>
    /// <response code="400">Session cannot be deleted</response>
    [HttpDelete("sessions/{sessionId}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public ActionResult DeleteSession(Guid sessionId)
    {
        if (!_activeSessions.TryGetValue(sessionId, out var sessionInfo))
        {
            return NotFound(new ProblemDetails
            {
                Title = "Session Not Found",
                Detail = $"Seeding session {sessionId} was not found",
                Status = StatusCodes.Status404NotFound
            });
        }

        if (sessionInfo.Status == "Running")
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Invalid Session State",
                Detail = $"Cannot delete a running session. Cancel it first.",
                Status = StatusCodes.Status400BadRequest
            });
        }

        _activeSessions.TryRemove(sessionId, out _);

        _logger.LogInformation("Seeding session {SessionId} deleted", sessionId);

        return NoContent();
    }

    private string GetCurrentUser()
    {
        // In a real application, this would extract user information from authentication context
        // For now, return a placeholder based on environment
        return $"{Environment.UserName}@{Environment.MachineName}";
    }
}

/// <summary>
/// Request model for creating a new seeding session
/// </summary>
public class CreateSeederSessionRequest
{
    /// <summary>
    /// Target environment for seeding
    /// </summary>
    public string? Environment { get; set; }

    /// <summary>
    /// Dealer identifier
    /// </summary>
    public string? DealerId { get; set; }

    /// <summary>
    /// Customer name
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// Number of drivers to generate
    /// </summary>
    public int DriversCount { get; set; }

    /// <summary>
    /// Number of vehicles to generate
    /// </summary>
    public int VehiclesCount { get; set; }
}


